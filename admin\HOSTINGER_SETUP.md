# Hostinger Setup Guide for InstantoPay Admin Panel

## 🚨 **Immediate Fix for 403 Forbidden Error**

### **Step 1: Upload Missing Files**
You need to upload these files that may be missing:

1. **Upload the updated `.htaccess` file** (it's a hidden file, so make sure it uploads)
2. **Create the required directories:**
   - `admin/cache/` (with 777 permissions)
   - `admin/logs/` (with 777 permissions)

### **Step 2: Set Correct File Permissions via Hostinger File Manager**

1. **Login to Hostinger Control Panel**
2. **Go to File Manager**
3. **Navigate to your domain folder → referbot → admin**
4. **Set these permissions:**

```
admin/                    → 755 (drwxr-xr-x)
admin/index.php          → 644 (-rw-r--r--)
admin/login.php          → 644 (-rw-r--r--)
admin/dashboard.php      → 644 (-rw-r--r--)
admin/users.php          → 644 (-rw-r--r--)
admin/financial.php      → 644 (-rw-r--r--)
admin/analytics.php      → 644 (-rw-r--r--)
admin/config.php         → 644 (-rw-r--r--)
admin/auth.php           → 644 (-rw-r--r--)
admin/DataProcessor.php  → 644 (-rw-r--r--)
admin/.htaccess          → 644 (-rw-r--r--)
admin/cache/             → 777 (drwxrwxrwx)
admin/logs/              → 777 (drwxrwxrwx)
```

### **Step 3: Hostinger-Specific Instructions**

#### **Via Hostinger File Manager:**
1. Right-click on each file/folder
2. Select "Permissions"
3. Set the permissions as listed above
4. For directories, check "Apply to subdirectories"

#### **Via FTP (if you prefer):**
```bash
chmod 755 admin/
chmod 644 admin/*.php
chmod 644 admin/.htaccess
chmod 777 admin/cache/
chmod 777 admin/logs/
```

### **Step 4: Test Access**

After setting permissions, try accessing:
1. `https://olivedrab-wren-455635.hostingersite.com/referbot/admin/`
2. `https://olivedrab-wren-455635.hostingersite.com/referbot/admin/login.php`

## 🔧 **Alternative Solutions if Still Getting 403**

### **Solution A: Create a Simple Test File**

Create `admin/test.php` with this content:
```php
<?php
echo "Admin panel directory is accessible!";
echo "<br>PHP Version: " . phpversion();
echo "<br>Current directory: " . __DIR__;
echo "<br>File permissions test: " . (is_writable(__DIR__ . '/cache') ? 'Cache writable' : 'Cache not writable');
?>
```

Test access: `https://olivedrab-wren-455635.hostingersite.com/referbot/admin/test.php`

### **Solution B: Simplified .htaccess**

If still getting 403, replace the `.htaccess` content with this minimal version:
```apache
# Minimal configuration for Hostinger
DirectoryIndex index.php login.php

# Protect sensitive files
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "auth.php">
    Order Allow,Deny
    Deny from all
</Files>
```

### **Solution C: Remove .htaccess Temporarily**

1. Rename `.htaccess` to `.htaccess.backup`
2. Test if admin panel loads
3. If it works, gradually add back .htaccess rules

## 🎯 **Hostinger-Specific Considerations**

### **1. PHP Version**
- Ensure PHP 7.4+ is selected in Hostinger control panel
- Go to: Advanced → PHP Configuration → Select PHP 7.4 or 8.0+

### **2. File Upload Issues**
- Hidden files (starting with .) sometimes don't upload via web interface
- Use FTP client like FileZilla for reliable upload of .htaccess

### **3. Directory Structure Verification**
Your structure should look like:
```
public_html/
└── referbot/
    ├── admin/
    │   ├── index.php
    │   ├── login.php
    │   ├── dashboard.php
    │   ├── users.php
    │   ├── financial.php
    │   ├── analytics.php
    │   ├── config.php
    │   ├── auth.php
    │   ├── DataProcessor.php
    │   ├── .htaccess
    │   ├── cache/ (777 permissions)
    │   └── logs/ (777 permissions)
    ├── config.php (your bot config)
    └── data/ (your bot data)
```

## 🚀 **Quick Fix Commands for Hostinger**

If you have SSH access (Premium plans), run these commands:

```bash
# Navigate to your admin directory
cd public_html/referbot/admin/

# Set correct permissions
chmod 755 .
chmod 644 *.php
chmod 644 .htaccess
chmod 777 cache/
chmod 777 logs/

# Verify permissions
ls -la

# Test PHP functionality
php -v
```

## 📞 **If Still Not Working**

### **Contact Hostinger Support**
If the 403 error persists, contact Hostinger support with this information:

**Subject:** "403 Forbidden Error on Admin Directory"

**Message:**
```
Hello,

I'm getting a 403 Forbidden error when accessing my admin panel at:
https://olivedrab-wren-455635.hostingersite.com/referbot/admin/

I have:
1. Set correct file permissions (755 for directories, 644 for files)
2. Created proper .htaccess file
3. Verified all files are uploaded correctly

The directory contains PHP files that should be accessible. Can you please check if there are any server-level restrictions blocking access to this directory?

Domain: olivedrab-wren-455635.hostingersite.com
Directory: /referbot/admin/
Error: 403 Forbidden

Thank you for your assistance.
```

### **Alternative Hosting Solutions**
If Hostinger continues to block access, consider:
1. Moving admin panel to a subdomain
2. Using a different directory name (e.g., `management` instead of `admin`)
3. Contacting Hostinger about their security policies

## ✅ **Success Checklist**

- [ ] All admin files uploaded correctly
- [ ] .htaccess file uploaded (check it's not .htaccess.txt)
- [ ] cache/ directory created with 777 permissions
- [ ] logs/ directory created with 777 permissions
- [ ] PHP 7.4+ selected in Hostinger control panel
- [ ] File permissions set correctly (755/644/777)
- [ ] Test file accessible
- [ ] Admin panel loads without 403 error

Once these steps are completed, you should be able to access:
`https://olivedrab-wren-455635.hostingersite.com/referbot/admin/`

And login with passcode: `1412`
