<?php
/**
 * Admin Panel Entry Point
 * Redirects to appropriate page based on authentication status
 */

define('ADMIN_PANEL_ACCESS', true);
require_once 'config.php';
require_once 'auth.php';

// Check authentication and redirect accordingly
if (AdminAuth::isAuthenticated()) {
    // User is authenticated, redirect to dashboard
    header('Location: dashboard.php');
} else {
    // User is not authenticated, redirect to login
    header('Location: login.php');
}

exit;
?>
