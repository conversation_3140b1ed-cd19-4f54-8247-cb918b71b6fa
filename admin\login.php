<?php
/**
 * Admin Login Page
 */

define('ADMIN_PANEL_ACCESS', true);
require_once 'config.php';
require_once 'auth.php';

// Redirect if already authenticated
if (AdminAuth::isAuthenticated()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$remainingTime = 0;

// Check if IP is rate limited
$ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if (!checkLoginAttempts($ip)) {
    $remainingTime = LOGIN_LOCKOUT_TIME;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ADMIN_PANEL_TITLE; ?> - Login</title>
    
    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 36px;
        }
        
        .form-control {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            padding: 12px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
        }
        
        .lockout-timer {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .security-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #4a5568;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .input-group-text {
            background: transparent;
            border: 2px solid #e2e8f0;
            border-right: none;
            border-radius: 12px 0 0 12px;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }
        
        .input-group:focus-within .input-group-text {
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">
                <i class="bi bi-shield-lock"></i>
            </div>
            <h2 class="fw-bold text-dark mb-2"><?php echo ADMIN_PANEL_TITLE; ?></h2>
            <p class="text-muted">Secure Admin Access</p>
        </div>
        
        <?php if ($remainingTime > 0): ?>
        <div class="lockout-timer" id="lockoutTimer">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Account Temporarily Locked</strong><br>
            Too many failed attempts. Try again in <span id="countdown"><?php echo floor($remainingTime / 60); ?>:<?php echo str_pad($remainingTime % 60, 2, '0', STR_PAD_LEFT); ?></span>
        </div>
        <?php endif; ?>
        
        <form id="loginForm" <?php echo $remainingTime > 0 ? 'style="display:none;"' : ''; ?>>
            <div class="mb-3">
                <label for="passcode" class="form-label fw-semibold">Admin Passcode</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-key"></i>
                    </span>
                    <input type="password" class="form-control" id="passcode" name="passcode" 
                           placeholder="Enter admin passcode" required autocomplete="current-password">
                </div>
            </div>
            
            <div class="d-grid mb-3">
                <button type="submit" class="btn btn-login text-white" id="loginBtn">
                    <span class="login-text">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        Login to Admin Panel
                    </span>
                    <span class="loading-spinner">
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        Authenticating...
                    </span>
                </button>
            </div>
            
            <div id="errorAlert" class="alert alert-danger d-none" role="alert">
                <i class="bi bi-exclamation-circle me-2"></i>
                <span id="errorMessage"></span>
            </div>
        </form>
        
        <div class="security-info">
            <div class="d-flex align-items-center mb-2">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Security Information</strong>
            </div>
            <ul class="mb-0 ps-3">
                <li>Maximum <?php echo MAX_LOGIN_ATTEMPTS; ?> login attempts allowed</li>
                <li>Account locks for <?php echo floor(LOGIN_LOCKOUT_TIME / 60); ?> minutes after failed attempts</li>
                <li>Session expires after <?php echo floor(ADMIN_SESSION_TIMEOUT / 60); ?> minutes of inactivity</li>
                <li>All login attempts are logged for security</li>
            </ul>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let lockoutTimer;
        let remainingTime = <?php echo $remainingTime; ?>;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Start lockout countdown if needed
            if (remainingTime > 0) {
                startLockoutCountdown();
            }
            
            // Focus on passcode input
            document.getElementById('passcode').focus();
            
            // Handle form submission
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            
            // Handle Enter key on passcode input
            document.getElementById('passcode').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleLogin(e);
                }
            });
        });
        
        function startLockoutCountdown() {
            updateCountdown();
            lockoutTimer = setInterval(function() {
                remainingTime--;
                updateCountdown();
                
                if (remainingTime <= 0) {
                    clearInterval(lockoutTimer);
                    document.getElementById('lockoutTimer').style.display = 'none';
                    document.getElementById('loginForm').style.display = 'block';
                    document.getElementById('passcode').focus();
                }
            }, 1000);
        }
        
        function updateCountdown() {
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            document.getElementById('countdown').textContent = 
                minutes + ':' + seconds.toString().padStart(2, '0');
        }
        
        function handleLogin(e) {
            e.preventDefault();
            
            const form = e.target;
            const passcode = document.getElementById('passcode').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorAlert = document.getElementById('errorAlert');
            
            if (!passcode.trim()) {
                showError('Please enter the admin passcode');
                return;
            }
            
            // Show loading state
            setLoadingState(true);
            hideError();
            
            // Prepare form data
            const formData = new FormData();
            formData.append('login', '1');
            formData.append('passcode', passcode);
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
            
            // Submit login request
            fetch('auth.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                setLoadingState(false);
                
                if (data.success) {
                    // Successful login
                    showSuccess('Login successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = 'dashboard.php';
                    }, 1000);
                } else {
                    // Failed login
                    showError(data.message || 'Login failed');
                    
                    // Clear passcode field
                    document.getElementById('passcode').value = '';
                    document.getElementById('passcode').focus();
                    
                    // Check if account is now locked
                    if (data.message && data.message.includes('Too many failed attempts')) {
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    }
                }
            })
            .catch(error => {
                setLoadingState(false);
                showError('Network error. Please try again.');
                console.error('Login error:', error);
            });
        }
        
        function setLoadingState(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const loginText = loginBtn.querySelector('.login-text');
            const loadingSpinner = loginBtn.querySelector('.loading-spinner');
            
            if (loading) {
                loginText.style.display = 'none';
                loadingSpinner.style.display = 'inline';
                loginBtn.disabled = true;
            } else {
                loginText.style.display = 'inline';
                loadingSpinner.style.display = 'none';
                loginBtn.disabled = false;
            }
        }
        
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            
            errorMessage.textContent = message;
            errorAlert.classList.remove('d-none');
            
            // Auto-hide after 5 seconds
            setTimeout(hideError, 5000);
        }
        
        function hideError() {
            const errorAlert = document.getElementById('errorAlert');
            errorAlert.classList.add('d-none');
        }
        
        function showSuccess(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            
            errorAlert.className = 'alert alert-success';
            errorMessage.innerHTML = '<i class="bi bi-check-circle me-2"></i>' + message;
            errorAlert.classList.remove('d-none');
        }
        
        // Prevent form resubmission on page refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
    </script>
</body>
</html>
