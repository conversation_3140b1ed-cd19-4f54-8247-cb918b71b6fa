<?php
/**
 * Financial Management Module
 * Comprehensive financial analytics and withdrawal tracking
 */

define('ADMIN_PANEL_ACCESS', true);
require_once 'config.php';
require_once 'auth.php';
require_once 'DataProcessor.php';

// Require authentication
AdminAuth::requireAuth();

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_GET['action']) {
            case 'get_financial_analytics':
                $analytics = DataProcessor::getFinancialAnalytics();
                echo json_encode(['success' => true, 'data' => $analytics]);
                break;
                
            case 'get_chart_data':
                $chartData = DataProcessor::getChartData();
                echo json_encode(['success' => true, 'data' => $chartData]);
                break;
                
            case 'get_withdrawal_trends':
                // Get withdrawal trends over time
                $trends = getWithdrawalTrends();
                echo json_encode(['success' => true, 'data' => $trends]);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Helper function to get withdrawal trends
function getWithdrawalTrends() {
    $users = DataProcessor::getAllUsers();
    $trends = [];
    
    // Initialize last 30 days
    for ($i = 29; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $trends[$date] = [
            'date' => $date,
            'withdrawals' => 0,
            'amount' => 0,
            'users' => 0
        ];
    }
    
    foreach ($users as $user) {
        $withdrawalReports = $user['withdrawal_report'] ?? [];
        foreach ($withdrawalReports as $withdrawal) {
            if (isset($withdrawal['timestamp'])) {
                $date = date('Y-m-d', $withdrawal['timestamp']);
                if (isset($trends[$date])) {
                    $trends[$date]['withdrawals']++;
                    $trends[$date]['amount'] += $withdrawal['amount'] ?? 0;
                    $trends[$date]['users']++;
                }
            }
        }
    }
    
    return array_values($trends);
}

// Get initial data
$financialAnalytics = DataProcessor::getFinancialAnalytics();
$chartData = DataProcessor::getChartData();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ADMIN_PANEL_TITLE; ?> - Financial Analytics</title>
    
    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
        }
        
        body {
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .financial-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
            overflow: hidden;
        }
        
        .financial-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .financial-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            margin-bottom: 15px;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .distribution-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 4px;
            background-color: #e5e7eb;
            overflow: hidden;
        }
        
        .progress-bar-custom {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .kpi-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .kpi-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .kpi-label {
            color: #6b7280;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .trend-indicator {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 12px;
            margin-top: 5px;
            display: inline-block;
        }
        
        .trend-up {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .trend-down {
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">
                <i class="bi bi-speedometer2 me-2"></i>
                <?php echo ADMIN_PANEL_TITLE; ?>
            </a>
            
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <a href="dashboard.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-house me-1"></i>
                    Dashboard
                </a>
                
                <a href="users.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-people me-1"></i>
                    Users
                </a>

                <a href="analytics.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-trophy me-1"></i>
                    Analytics
                </a>
                
                <div class="dropdown">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        Admin
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Financial Overview Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="metric-card">
                    <h2 class="mb-4">
                        <i class="bi bi-graph-up me-2"></i>
                        Financial Analytics Dashboard
                    </h2>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-value">₹<?php echo number_format($financialAnalytics['total_balance'], 2); ?></div>
                            <div class="metric-label">Total User Balance</div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-value">₹<?php echo number_format($financialAnalytics['total_withdrawals'], 2); ?></div>
                            <div class="metric-label">Total Withdrawals</div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-value">₹<?php echo number_format($financialAnalytics['pending_withdrawals'], 2); ?></div>
                            <div class="metric-label">Pending Withdrawals</div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-value">₹<?php echo number_format($financialAnalytics['commission_earned'], 2); ?></div>
                            <div class="metric-label">Commission Earned</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="kpi-grid">
            <div class="kpi-card">
                <div class="financial-icon bg-success mx-auto">
                    <i class="bi bi-currency-rupee"></i>
                </div>
                <div class="kpi-value text-success">₹<?php echo number_format($financialAnalytics['average_balance'], 2); ?></div>
                <div class="kpi-label">Average Balance per User</div>
                <div class="trend-indicator trend-up">
                    <i class="bi bi-arrow-up"></i> Healthy
                </div>
            </div>
            
            <div class="kpi-card">
                <div class="financial-icon bg-info mx-auto">
                    <i class="bi bi-percent"></i>
                </div>
                <div class="kpi-value text-info"><?php echo number_format($financialAnalytics['withdrawal_success_rate'], 1); ?>%</div>
                <div class="kpi-label">Withdrawal Success Rate</div>
                <div class="trend-indicator trend-up">
                    <i class="bi bi-arrow-up"></i> Excellent
                </div>
            </div>
            
            <div class="kpi-card">
                <div class="financial-icon bg-warning mx-auto">
                    <i class="bi bi-gift"></i>
                </div>
                <div class="kpi-value text-warning">₹<?php echo number_format($financialAnalytics['total_earnings_paid'], 2); ?></div>
                <div class="kpi-label">Total Referral Rewards Paid</div>
                <div class="trend-indicator trend-up">
                    <i class="bi bi-arrow-up"></i> Growing
                </div>
            </div>
            
            <div class="kpi-card">
                <div class="financial-icon bg-primary mx-auto">
                    <i class="bi bi-calculator"></i>
                </div>
                <div class="kpi-value text-primary">₹<?php echo number_format($financialAnalytics['total_balance'] + $financialAnalytics['total_withdrawals'], 2); ?></div>
                <div class="kpi-label">Total Money Circulated</div>
                <div class="trend-indicator trend-up">
                    <i class="bi bi-arrow-up"></i> Strong
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-graph-up me-2"></i>
                        Financial Overview Comparison
                    </h5>
                    <canvas id="financialComparisonChart" height="100"></canvas>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-pie-chart me-2"></i>
                        Balance Distribution
                    </h5>
                    <canvas id="balanceDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Withdrawal Trends -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-bar-chart me-2"></i>
                        Withdrawal Trends (Last 30 Days)
                    </h5>
                    <canvas id="withdrawalTrendsChart" height="80"></canvas>
                </div>
            </div>
        </div>

        <!-- Balance Distribution Details -->
        <div class="row">
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-list-ul me-2"></i>
                        Balance Distribution Breakdown
                    </h5>
                    <div id="balanceBreakdown">
                        <?php 
                        $total = array_sum($financialAnalytics['balance_distribution']);
                        foreach ($financialAnalytics['balance_distribution'] as $range => $count): 
                            $percentage = $total > 0 ? ($count / $total) * 100 : 0;
                        ?>
                        <div class="distribution-item">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="fw-semibold">₹<?php echo $range; ?></span>
                                    <span class="text-muted"><?php echo number_format($count); ?> users (<?php echo number_format($percentage, 1); ?>%)</span>
                                </div>
                                <div class="progress-custom">
                                    <div class="progress-bar-custom bg-primary" style="width: <?php echo $percentage; ?>%"></div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-calculator me-2"></i>
                        Financial Calculations
                    </h5>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Revenue Breakdown</h6>
                                    <table class="table table-sm mb-0">
                                        <tr>
                                            <td>Commission from Withdrawals (10%)</td>
                                            <td class="text-end fw-bold">₹<?php echo number_format($financialAnalytics['commission_earned'], 2); ?></td>
                                        </tr>
                                        <tr>
                                            <td>Referral Rewards Paid</td>
                                            <td class="text-end text-danger">-₹<?php echo number_format($financialAnalytics['total_earnings_paid'], 2); ?></td>
                                        </tr>
                                        <tr class="border-top">
                                            <td class="fw-bold">Net Revenue</td>
                                            <td class="text-end fw-bold text-success">₹<?php echo number_format($financialAnalytics['commission_earned'] - $financialAnalytics['total_earnings_paid'], 2); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Platform Health Metrics</h6>
                                    <table class="table table-sm mb-0">
                                        <tr>
                                            <td>Money Velocity</td>
                                            <td class="text-end">
                                                <?php 
                                                $velocity = $financialAnalytics['total_balance'] > 0 ? 
                                                    ($financialAnalytics['total_withdrawals'] / $financialAnalytics['total_balance']) * 100 : 0;
                                                echo number_format($velocity, 1) . '%';
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Liquidity Ratio</td>
                                            <td class="text-end">
                                                <?php 
                                                $liquidity = $financialAnalytics['pending_withdrawals'] > 0 ? 
                                                    ($financialAnalytics['total_balance'] / $financialAnalytics['pending_withdrawals']) : 0;
                                                echo number_format($liquidity, 2) . 'x';
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Platform Efficiency</td>
                                            <td class="text-end text-success">
                                                <?php echo number_format($financialAnalytics['withdrawal_success_rate'], 1); ?>%
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let charts = {};

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();

            // Auto-refresh every 5 minutes
            setInterval(refreshFinancialData, 300000);
        });

        // Initialize all charts
        function initializeCharts() {
            const financialData = <?php echo json_encode($financialAnalytics); ?>;
            const chartData = <?php echo json_encode($chartData); ?>;

            // Financial Comparison Chart
            const comparisonCtx = document.getElementById('financialComparisonChart').getContext('2d');
            charts.comparison = new Chart(comparisonCtx, {
                type: 'bar',
                data: {
                    labels: ['Total Balance', 'Successful Withdrawals', 'Pending Withdrawals', 'Referral Rewards'],
                    datasets: [{
                        label: 'Amount (₹)',
                        data: [
                            financialData.total_balance,
                            financialData.total_withdrawals,
                            financialData.pending_withdrawals,
                            financialData.total_earnings_paid
                        ],
                        backgroundColor: [
                            'rgba(8, 145, 178, 0.8)',
                            'rgba(5, 150, 105, 0.8)',
                            'rgba(217, 119, 6, 0.8)',
                            'rgba(139, 69, 19, 0.8)'
                        ],
                        borderColor: [
                            'rgb(8, 145, 178)',
                            'rgb(5, 150, 105)',
                            'rgb(217, 119, 6)',
                            'rgb(139, 69, 19)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return '₹' + context.parsed.y.toLocaleString('en-IN');
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString('en-IN');
                                }
                            }
                        }
                    }
                }
            });

            // Balance Distribution Chart
            const distributionCtx = document.getElementById('balanceDistributionChart').getContext('2d');
            charts.distribution = new Chart(distributionCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(financialData.balance_distribution),
                    datasets: [{
                        data: Object.values(financialData.balance_distribution),
                        backgroundColor: [
                            'rgb(239, 68, 68)',
                            'rgb(245, 158, 11)',
                            'rgb(34, 197, 94)',
                            'rgb(59, 130, 246)',
                            'rgb(147, 51, 234)'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed + ' users (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });

            // Load withdrawal trends
            loadWithdrawalTrends();
        }

        // Load withdrawal trends data
        function loadWithdrawalTrends() {
            fetch('financial.php?action=get_withdrawal_trends')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        createWithdrawalTrendsChart(data.data);
                    }
                })
                .catch(error => console.error('Error loading withdrawal trends:', error));
        }

        // Create withdrawal trends chart
        function createWithdrawalTrendsChart(trendsData) {
            const ctx = document.getElementById('withdrawalTrendsChart').getContext('2d');

            charts.withdrawalTrends = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: trendsData.map(item => {
                        const date = new Date(item.date);
                        return date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
                    }),
                    datasets: [
                        {
                            label: 'Withdrawal Amount (₹)',
                            data: trendsData.map(item => item.amount),
                            borderColor: 'rgb(5, 150, 105)',
                            backgroundColor: 'rgba(5, 150, 105, 0.1)',
                            tension: 0.4,
                            fill: true,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Number of Withdrawals',
                            data: trendsData.map(item => item.withdrawals),
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: false,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    if (context.datasetIndex === 0) {
                                        return 'Amount: ₹' + context.parsed.y.toLocaleString('en-IN');
                                    } else {
                                        return 'Count: ' + context.parsed.y;
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Amount (₹)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString('en-IN');
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Count'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        // Refresh financial data
        function refreshFinancialData() {
            Promise.all([
                fetch('financial.php?action=get_financial_analytics').then(r => r.json()),
                fetch('financial.php?action=get_withdrawal_trends').then(r => r.json())
            ]).then(([analytics, trends]) => {
                if (analytics.success) {
                    updateFinancialMetrics(analytics.data);
                }
                if (trends.success) {
                    updateWithdrawalTrends(trends.data);
                }
            }).catch(error => {
                console.error('Error refreshing financial data:', error);
            });
        }

        // Update financial metrics
        function updateFinancialMetrics(data) {
            // Update comparison chart
            charts.comparison.data.datasets[0].data = [
                data.total_balance,
                data.total_withdrawals,
                data.pending_withdrawals,
                data.total_earnings_paid
            ];
            charts.comparison.update();

            // Update distribution chart
            charts.distribution.data.datasets[0].data = Object.values(data.balance_distribution);
            charts.distribution.update();
        }

        // Update withdrawal trends
        function updateWithdrawalTrends(trendsData) {
            charts.withdrawalTrends.data.labels = trendsData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
            });
            charts.withdrawalTrends.data.datasets[0].data = trendsData.map(item => item.amount);
            charts.withdrawalTrends.data.datasets[1].data = trendsData.map(item => item.withdrawals);
            charts.withdrawalTrends.update();
        }

        // Utility functions
        function formatCurrency(amount) {
            return '₹' + parseFloat(amount || 0).toLocaleString('en-IN', {minimumFractionDigits: 2});
        }

        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toLocaleString();
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('auth.php?action=logout')
                    .then(() => {
                        window.location.href = 'login.php';
                    });
            }
        }
    </script>
</body>
</html>
