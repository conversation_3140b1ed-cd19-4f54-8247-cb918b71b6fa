<?php
/**
 * Authentication System for Admin Panel
 * Handles secure login, session management, and access control
 */

define('ADMIN_PANEL_ACCESS', true);
require_once 'config.php';

class AdminAuth {
    
    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated() {
        return isset($_SESSION['admin_authenticated']) && 
               $_SESSION['admin_authenticated'] === true &&
               isset($_SESSION['admin_login_time']) &&
               (time() - $_SESSION['admin_login_time']) < ADMIN_SESSION_TIMEOUT;
    }
    
    /**
     * Authenticate user with passcode
     */
    public static function authenticate($passcode) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        // Check rate limiting
        if (!checkLoginAttempts($ip)) {
            logAdminAction('LOGIN_BLOCKED', 'Too many failed attempts from IP: ' . $ip);
            return [
                'success' => false,
                'message' => 'Too many failed login attempts. Please try again in ' . 
                           floor(LOGIN_LOCKOUT_TIME / 60) . ' minutes.'
            ];
        }
        
        // Validate passcode
        if ($passcode === ADMIN_PASSCODE) {
            // Successful login
            $_SESSION['admin_authenticated'] = true;
            $_SESSION['admin_login_time'] = time();
            $_SESSION['admin_ip'] = $ip;
            $_SESSION['admin_user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // Clear any failed attempts
            $attempts_file = ADMIN_CACHE_DIR . '/login_attempts_' . md5($ip) . '.json';
            if (file_exists($attempts_file)) {
                unlink($attempts_file);
            }
            
            logAdminAction('LOGIN_SUCCESS', 'Successful admin login');
            
            return [
                'success' => true,
                'message' => 'Login successful'
            ];
        } else {
            // Failed login
            recordLoginAttempt($ip);
            logAdminAction('LOGIN_FAILED', 'Invalid passcode attempt');
            
            return [
                'success' => false,
                'message' => 'Invalid passcode'
            ];
        }
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        logAdminAction('LOGOUT', 'Admin logout');
        
        // Clear session data
        $_SESSION = [];
        
        // Destroy session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        session_destroy();
    }
    
    /**
     * Require authentication - redirect to login if not authenticated
     */
    public static function requireAuth() {
        if (!self::isAuthenticated()) {
            // Check for session hijacking
            if (isset($_SESSION['admin_ip']) && $_SESSION['admin_ip'] !== ($_SERVER['REMOTE_ADDR'] ?? '')) {
                logAdminAction('SECURITY_VIOLATION', 'IP address mismatch detected');
                self::logout();
            }
            
            header('Location: login.php');
            exit;
        }
        
        // Update last activity time
        $_SESSION['admin_last_activity'] = time();
    }
    
    /**
     * Get remaining session time
     */
    public static function getSessionTimeRemaining() {
        if (!self::isAuthenticated()) {
            return 0;
        }
        
        $elapsed = time() - $_SESSION['admin_login_time'];
        return max(0, ADMIN_SESSION_TIMEOUT - $elapsed);
    }
    
    /**
     * Extend session if user is active
     */
    public static function extendSession() {
        if (self::isAuthenticated()) {
            $_SESSION['admin_login_time'] = time();
            return true;
        }
        return false;
    }
}

// Handle AJAX requests for session management
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['action']) {
        case 'check_session':
            echo json_encode([
                'authenticated' => AdminAuth::isAuthenticated(),
                'time_remaining' => AdminAuth::getSessionTimeRemaining()
            ]);
            break;
            
        case 'extend_session':
            echo json_encode([
                'success' => AdminAuth::extendSession(),
                'time_remaining' => AdminAuth::getSessionTimeRemaining()
            ]);
            break;
            
        case 'logout':
            AdminAuth::logout();
            echo json_encode(['success' => true]);
            break;
            
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    exit;
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    header('Content-Type: application/json');
    
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid security token. Please refresh the page.'
        ]);
        exit;
    }
    
    $passcode = $_POST['passcode'] ?? '';
    $result = AdminAuth::authenticate($passcode);
    
    echo json_encode($result);
    exit;
}
?>
