# InstantoPay Admin Panel - Hostinger Compatible Configuration

# Set default index files
DirectoryIndex index.php login.php

# Basic security for sensitive files
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "auth.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "DataProcessor.php">
    Order Allow,Deny
    Deny from all
</Files>

# Security Headers (Hostinger compatible)
<IfModule mod_headers.c>
    # Prevent clickjacking (less restrictive for admin panel)
    Header always append X-Frame-Options SAMEORIGIN

    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to version control files
<FilesMatch "\.(git|svn|hg)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
</IfModule>

# Prevent hotlinking
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirect to HTTPS if available
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP:X-Forwarded-Proto} !https
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Prevent hotlinking of images
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|ico)$ - [F]
</IfModule>

# Limit file upload size (if applicable)
<IfModule mod_php.c>
    php_value upload_max_filesize 2M
    php_value post_max_size 2M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
</IfModule>

# Error pages - Updated for renamed directory (change 'management' to your chosen name)
ErrorDocument 403 /referbot/management/login.php
ErrorDocument 404 /referbot/management/login.php
ErrorDocument 500 /referbot/management/login.php

# Disable directory browsing
Options -Indexes

# Prevent access to PHP error logs
<Files "error_log">
    Order Allow,Deny
    Deny from all
</Files>

<Files "error.log">
    Order Allow,Deny
    Deny from all
</Files>

# Additional security measures
<IfModule mod_rewrite.c>
    # Block suspicious requests
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code\(.*\) [OR]
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\|%7C) [OR]
    RewriteCond %{QUERY_STRING} gopher:// [OR]
    RewriteCond %{QUERY_STRING} javascript: [OR]
    RewriteCond %{QUERY_STRING} ftp:// [OR]
    RewriteCond %{QUERY_STRING} (\||%7C) [OR]
    RewriteCond %{QUERY_STRING} \.\./ [OR]
    RewriteCond %{QUERY_STRING} http\:/ [OR]
    RewriteCond %{QUERY_STRING} https\:/ [OR]
    RewriteCond %{QUERY_STRING} \=PHP[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12} [OR]
    RewriteCond %{QUERY_STRING} (\.\./|\.\.) [OR]
    RewriteCond %{QUERY_STRING} ftp\: [OR]
    RewriteCond %{QUERY_STRING} http\: [OR]
    RewriteCond %{QUERY_STRING} https\: [OR]
    RewriteCond %{QUERY_STRING} \=\|w\| [OR]
    RewriteCond %{QUERY_STRING} ^(.*)/self/(.*)$ [OR]
    RewriteCond %{QUERY_STRING} ^(.*)cPath=http://(.*)$ [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^e]*e)+mbed.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^o]*o)+bject.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^i]*i)+frame.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} base64_encode.*\(.*\) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>).* [OR]
    RewriteCond %{QUERY_STRING} (NULL|OUTFILE|LOAD_FILE) [OR]
    RewriteCond %{QUERY_STRING} (\.{1,}/)+(motd|etc|bin) [OR]
    RewriteCond %{QUERY_STRING} (localhost|loopback|127\.0\.0\.1) [OR]
    RewriteCond %{QUERY_STRING} (<|>|'|%0A|%0D|%27|%3C|%3E|%00) [OR]
    RewriteCond %{QUERY_STRING} concat[^\(]*\( [OR]
    RewriteCond %{QUERY_STRING} union([^s]*s)+elect [OR]
    RewriteCond %{QUERY_STRING} union([^a]*a)+ll([^s]*s)+elect [OR]
    RewriteCond %{QUERY_STRING} (;|<|>|'|"|\)|%0A|%0D|%22|%27|%3C|%3E|%00).*(/\*|union|select|insert|drop|delete|update|cast|create|char|convert|alter|declare|order|script|set|md5|benchmark|encode) [OR]
    RewriteCond %{QUERY_STRING} (sp_executesql) [OR]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>
