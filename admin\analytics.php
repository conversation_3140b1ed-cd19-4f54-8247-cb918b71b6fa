<?php
/**
 * Analytics and Leaderboards Page
 * Comprehensive bot analytics with interactive charts and performance metrics
 */

define('ADMIN_PANEL_ACCESS', true);
require_once 'config.php';
require_once 'auth.php';
require_once 'DataProcessor.php';

// Require authentication
AdminAuth::requireAuth();

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_GET['action']) {
            case 'get_leaderboards':
                $leaderboards = DataProcessor::getLeaderboards();
                echo json_encode(['success' => true, 'data' => $leaderboards]);
                break;
                
            case 'get_analytics':
                $analytics = getBotAnalytics();
                echo json_encode(['success' => true, 'data' => $analytics]);
                break;
                
            case 'get_growth_metrics':
                $metrics = getGrowthMetrics();
                echo json_encode(['success' => true, 'data' => $metrics]);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Helper functions for analytics
function getBotAnalytics() {
    $users = DataProcessor::getAllUsers();
    $stats = DataProcessor::getUserStatistics();
    
    $analytics = [
        'user_engagement' => [
            'total_users' => $stats['total_users'],
            'active_users' => $stats['active_users'],
            'engagement_rate' => $stats['total_users'] > 0 ? ($stats['active_users'] / $stats['total_users']) * 100 : 0
        ],
        'referral_metrics' => [
            'total_referrals' => $stats['total_referrals'],
            'referred_users' => $stats['referred_users'],
            'conversion_rate' => $stats['total_users'] > 0 ? ($stats['referred_users'] / $stats['total_users']) * 100 : 0,
            'avg_referrals_per_user' => $stats['total_users'] > 0 ? $stats['total_referrals'] / $stats['total_users'] : 0
        ],
        'financial_health' => [
            'total_balance' => $stats['total_balance'],
            'total_withdrawals' => $stats['total_withdrawals'],
            'pending_withdrawals' => $stats['pending_withdrawals'],
            'money_velocity' => $stats['total_balance'] > 0 ? ($stats['total_withdrawals'] / $stats['total_balance']) * 100 : 0
        ]
    ];
    
    return $analytics;
}

function getGrowthMetrics() {
    $users = DataProcessor::getAllUsers();
    
    // Calculate growth metrics for last 30 days
    $growthData = [];
    for ($i = 29; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $growthData[$date] = [
            'new_users' => 0,
            'new_referrals' => 0,
            'total_users' => 0
        ];
    }
    
    $cumulativeUsers = 0;
    foreach ($users as $user) {
        $userId = $user['user_id'] ?? 0;
        if ($userId > 0) {
            $userDate = date('Y-m-d', $userId);
            if (isset($growthData[$userDate])) {
                $growthData[$userDate]['new_users']++;
            }
        }
        
        // Count referrals
        $promotionReports = $user['promotion_report'] ?? [];
        foreach ($promotionReports as $report) {
            if (isset($report['timestamp'])) {
                $referralDate = date('Y-m-d', $report['timestamp']);
                if (isset($growthData[$referralDate])) {
                    $growthData[$referralDate]['new_referrals']++;
                }
            }
        }
        
        $cumulativeUsers++;
    }
    
    // Calculate cumulative totals
    $runningTotal = 0;
    foreach ($growthData as $date => &$data) {
        $runningTotal += $data['new_users'];
        $data['total_users'] = $runningTotal;
    }
    
    return array_values($growthData);
}

// Get initial data
$leaderboards = DataProcessor::getLeaderboards();
$analytics = getBotAnalytics();
$growthMetrics = getGrowthMetrics();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ADMIN_PANEL_TITLE; ?> - Analytics & Leaderboards</title>
    
    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
        }
        
        body {
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .analytics-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
            overflow: hidden;
        }
        
        .analytics-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .metric-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .leaderboard-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .leaderboard-header {
            background: linear-gradient(135deg, var(--success-color), #047857);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }
        
        .leaderboard-item {
            padding: 15px 20px;
            border-bottom: 1px solid #e5e7eb;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
        }
        
        .leaderboard-item:hover {
            background-color: #f9fafb;
        }
        
        .leaderboard-item:last-child {
            border-bottom: none;
        }
        
        .rank-badge {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
            margin-right: 15px;
        }
        
        .rank-1 { background: linear-gradient(135deg, #ffd700, #ffed4e); color: #92400e; }
        .rank-2 { background: linear-gradient(135deg, #c0c0c0, #e5e7eb); color: #374151; }
        .rank-3 { background: linear-gradient(135deg, #cd7f32, #d97706); color: white; }
        .rank-other { background: linear-gradient(135deg, #6b7280, #9ca3af); }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .performance-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .indicator-excellent { background: #dcfce7; color: #166534; }
        .indicator-good { background: #dbeafe; color: #1e40af; }
        .indicator-average { background: #fef3c7; color: #92400e; }
        .indicator-poor { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">
                <i class="bi bi-speedometer2 me-2"></i>
                <?php echo ADMIN_PANEL_TITLE; ?>
            </a>
            
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <a href="dashboard.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-house me-1"></i>
                    Dashboard
                </a>
                
                <a href="users.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-people me-1"></i>
                    Users
                </a>
                
                <a href="financial.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-graph-up me-1"></i>
                    Financial
                </a>
                
                <div class="dropdown">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        Admin
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Analytics Overview -->
        <div class="analytics-grid">
            <div class="analytics-card">
                <div class="metric-header">
                    <div class="metric-value"><?php echo number_format($analytics['user_engagement']['engagement_rate'], 1); ?>%</div>
                    <div class="metric-label">User Engagement Rate</div>
                </div>
                <div class="p-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Active Users:</span>
                        <strong><?php echo number_format($analytics['user_engagement']['active_users']); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Total Users:</span>
                        <strong><?php echo number_format($analytics['user_engagement']['total_users']); ?></strong>
                    </div>
                    <div class="mt-3">
                        <?php 
                        $engagementRate = $analytics['user_engagement']['engagement_rate'];
                        if ($engagementRate >= 80) {
                            echo '<span class="performance-indicator indicator-excellent">Excellent</span>';
                        } elseif ($engagementRate >= 60) {
                            echo '<span class="performance-indicator indicator-good">Good</span>';
                        } elseif ($engagementRate >= 40) {
                            echo '<span class="performance-indicator indicator-average">Average</span>';
                        } else {
                            echo '<span class="performance-indicator indicator-poor">Needs Improvement</span>';
                        }
                        ?>
                    </div>
                </div>
            </div>
            
            <div class="analytics-card">
                <div class="metric-header">
                    <div class="metric-value"><?php echo number_format($analytics['referral_metrics']['conversion_rate'], 1); ?>%</div>
                    <div class="metric-label">Referral Conversion Rate</div>
                </div>
                <div class="p-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Referred Users:</span>
                        <strong><?php echo number_format($analytics['referral_metrics']['referred_users']); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Total Referrals:</span>
                        <strong><?php echo number_format($analytics['referral_metrics']['total_referrals']); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Avg per User:</span>
                        <strong><?php echo number_format($analytics['referral_metrics']['avg_referrals_per_user'], 1); ?></strong>
                    </div>
                </div>
            </div>
            
            <div class="analytics-card">
                <div class="metric-header">
                    <div class="metric-value"><?php echo number_format($analytics['financial_health']['money_velocity'], 1); ?>%</div>
                    <div class="metric-label">Money Velocity</div>
                </div>
                <div class="p-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Total Balance:</span>
                        <strong>₹<?php echo number_format($analytics['financial_health']['total_balance'], 2); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Withdrawals:</span>
                        <strong>₹<?php echo number_format($analytics['financial_health']['total_withdrawals'], 2); ?></strong>
                    </div>
                    <div class="mt-3">
                        <?php 
                        $velocity = $analytics['financial_health']['money_velocity'];
                        if ($velocity >= 50) {
                            echo '<span class="performance-indicator indicator-excellent">High Activity</span>';
                        } elseif ($velocity >= 30) {
                            echo '<span class="performance-indicator indicator-good">Good Flow</span>';
                        } elseif ($velocity >= 15) {
                            echo '<span class="performance-indicator indicator-average">Moderate</span>';
                        } else {
                            echo '<span class="performance-indicator indicator-poor">Low Activity</span>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Growth Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-graph-up me-2"></i>
                        User Growth & Referral Trends (Last 30 Days)
                    </h5>
                    <canvas id="growthChart" height="80"></canvas>
                </div>
            </div>
        </div>

        <!-- Leaderboards -->
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="leaderboard-card">
                    <div class="leaderboard-header">
                        <i class="bi bi-trophy me-2"></i>
                        Top Referrers
                    </div>
                    <div id="topReferrersLeaderboard">
                        <?php foreach (array_slice($leaderboards['top_referrers'], 0, 10) as $index => $user): ?>
                        <div class="leaderboard-item">
                            <div class="rank-badge rank-<?php echo $index < 3 ? $index + 1 : 'other'; ?>">
                                <?php echo $index + 1; ?>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold"><?php echo htmlspecialchars($user['name']); ?></div>
                                <?php if ($user['username']): ?>
                                    <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                <?php endif; ?>
                                <div class="mt-1">
                                    <span class="badge bg-primary me-1"><?php echo $user['referrals']; ?> referrals</span>
                                    <span class="badge bg-success">₹<?php echo number_format($user['earnings'], 2); ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="leaderboard-card">
                    <div class="leaderboard-header">
                        <i class="bi bi-cash-stack me-2"></i>
                        Top Withdrawers
                    </div>
                    <div id="topWithdrawersLeaderboard">
                        <?php foreach (array_slice($leaderboards['top_withdrawers'], 0, 10) as $index => $user): ?>
                        <div class="leaderboard-item">
                            <div class="rank-badge rank-<?php echo $index < 3 ? $index + 1 : 'other'; ?>">
                                <?php echo $index + 1; ?>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold"><?php echo htmlspecialchars($user['name']); ?></div>
                                <?php if ($user['username']): ?>
                                    <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                <?php endif; ?>
                                <div class="mt-1">
                                    <span class="badge bg-success me-1">₹<?php echo number_format($user['amount'], 2); ?></span>
                                    <span class="badge bg-info"><?php echo $user['withdrawal_count']; ?> withdrawals</span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="leaderboard-card">
                    <div class="leaderboard-header">
                        <i class="bi bi-wallet me-2"></i>
                        Top Balances
                    </div>
                    <div id="topBalancesLeaderboard">
                        <?php foreach (array_slice($leaderboards['top_balances'], 0, 10) as $index => $user): ?>
                        <div class="leaderboard-item">
                            <div class="rank-badge rank-<?php echo $index < 3 ? $index + 1 : 'other'; ?>">
                                <?php echo $index + 1; ?>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold"><?php echo htmlspecialchars($user['name']); ?></div>
                                <?php if ($user['username']): ?>
                                    <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                <?php endif; ?>
                                <div class="mt-1">
                                    <span class="badge bg-info">₹<?php echo number_format($user['balance'], 2); ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let charts = {};

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();

            // Auto-refresh every 5 minutes
            setInterval(refreshAnalytics, 300000);
        });

        // Initialize charts
        function initializeCharts() {
            const growthData = <?php echo json_encode($growthMetrics); ?>;

            // Growth Chart
            const growthCtx = document.getElementById('growthChart').getContext('2d');
            charts.growth = new Chart(growthCtx, {
                type: 'line',
                data: {
                    labels: growthData.map(item => {
                        const date = new Date(item.date || new Date());
                        return date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
                    }),
                    datasets: [
                        {
                            label: 'New Users',
                            data: growthData.map(item => item.new_users || 0),
                            borderColor: 'rgb(37, 99, 235)',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            tension: 0.4,
                            fill: true,
                            yAxisID: 'y'
                        },
                        {
                            label: 'New Referrals',
                            data: growthData.map(item => item.new_referrals || 0),
                            borderColor: 'rgb(5, 150, 105)',
                            backgroundColor: 'rgba(5, 150, 105, 0.1)',
                            tension: 0.4,
                            fill: false,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Cumulative Users',
                            data: growthData.map(item => item.total_users || 0),
                            borderColor: 'rgb(217, 119, 6)',
                            backgroundColor: 'rgba(217, 119, 6, 0.1)',
                            tension: 0.4,
                            fill: false,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Daily Count'
                            },
                            beginAtZero: true
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Cumulative Total'
                            },
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        // Refresh analytics data
        function refreshAnalytics() {
            Promise.all([
                fetch('analytics.php?action=get_leaderboards').then(r => r.json()),
                fetch('analytics.php?action=get_analytics').then(r => r.json()),
                fetch('analytics.php?action=get_growth_metrics').then(r => r.json())
            ]).then(([leaderboards, analytics, growth]) => {
                if (leaderboards.success) {
                    updateLeaderboards(leaderboards.data);
                }
                if (analytics.success) {
                    updateAnalyticsMetrics(analytics.data);
                }
                if (growth.success) {
                    updateGrowthChart(growth.data);
                }
            }).catch(error => {
                console.error('Error refreshing analytics:', error);
            });
        }

        // Update leaderboards
        function updateLeaderboards(data) {
            updateLeaderboard('topReferrersLeaderboard', data.top_referrers, 'referrers');
            updateLeaderboard('topWithdrawersLeaderboard', data.top_withdrawers, 'withdrawers');
            updateLeaderboard('topBalancesLeaderboard', data.top_balances, 'balances');
        }

        // Update individual leaderboard
        function updateLeaderboard(containerId, users, type) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            users.slice(0, 10).forEach((user, index) => {
                const item = document.createElement('div');
                item.className = 'leaderboard-item';

                let badges = '';
                switch (type) {
                    case 'referrers':
                        badges = `
                            <span class="badge bg-primary me-1">${user.referrals} referrals</span>
                            <span class="badge bg-success">₹${formatNumber(user.earnings)}</span>
                        `;
                        break;
                    case 'withdrawers':
                        badges = `
                            <span class="badge bg-success me-1">₹${formatNumber(user.amount)}</span>
                            <span class="badge bg-info">${user.withdrawal_count} withdrawals</span>
                        `;
                        break;
                    case 'balances':
                        badges = `<span class="badge bg-info">₹${formatNumber(user.balance)}</span>`;
                        break;
                }

                item.innerHTML = `
                    <div class="rank-badge rank-${index < 3 ? index + 1 : 'other'}">
                        ${index + 1}
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-semibold">${escapeHtml(user.name)}</div>
                        ${user.username ? `<small class="text-muted">@${escapeHtml(user.username)}</small>` : ''}
                        <div class="mt-1">${badges}</div>
                    </div>
                `;

                container.appendChild(item);
            });
        }

        // Update analytics metrics
        function updateAnalyticsMetrics(data) {
            // Update engagement rate
            const engagementElement = document.querySelector('.metric-value');
            if (engagementElement) {
                engagementElement.textContent = data.user_engagement.engagement_rate.toFixed(1) + '%';
            }

            // Update other metrics as needed
            // This would involve updating the DOM elements with new values
        }

        // Update growth chart
        function updateGrowthChart(growthData) {
            charts.growth.data.labels = growthData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
            });

            charts.growth.data.datasets[0].data = growthData.map(item => item.new_users);
            charts.growth.data.datasets[1].data = growthData.map(item => item.new_referrals);
            charts.growth.data.datasets[2].data = growthData.map(item => item.total_users);

            charts.growth.update();
        }

        // Utility functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toLocaleString();
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('auth.php?action=logout')
                    .then(() => {
                        window.location.href = 'login.php';
                    });
            }
        }
    </script>
</body>
</html>
