<?php
/**
 * User Management Interface
 * Advanced search, filtering, pagination, and user actions
 */

define('ADMIN_PANEL_ACCESS', true);
require_once 'config.php';
require_once 'auth.php';
require_once 'DataProcessor.php';

// Require authentication
AdminAuth::requireAuth();

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_GET['action']) {
            case 'get_users':
                $page = intval($_GET['page'] ?? 1);
                $pageSize = min(intval($_GET['pageSize'] ?? DEFAULT_PAGE_SIZE), MAX_PAGE_SIZE);
                $search = $_GET['search'] ?? '';
                $filters = [
                    'status' => $_GET['status'] ?? '',
                    'referral_status' => $_GET['referral_status'] ?? ''
                ];

                $result = DataProcessor::getPaginatedUsers($page, $pageSize, $search, $filters);
                echo json_encode(['success' => true, 'data' => $result]);
                break;

            case 'search_users':
                $query = $_GET['query'] ?? '';
                $limit = min(intval($_GET['limit'] ?? 50), 100);

                $results = DataProcessor::searchUsers($query, $limit);
                echo json_encode(['success' => true, 'data' => $results]);
                break;

            case 'get_user_details':
                $userId = $_GET['user_id'] ?? '';
                if (!$userId) {
                    throw new Exception('User ID required');
                }

                $user = getUser($userId);
                if (!$user) {
                    throw new Exception('User not found');
                }

                // Add calculated fields
                $user['referral_count'] = count($user['promotion_report'] ?? []);
                $user['withdrawal_count'] = count($user['withdrawal_report'] ?? []);
                $user['total_referral_earnings'] = array_sum(array_column($user['promotion_report'] ?? [], 'amount_got'));

                echo json_encode(['success' => true, 'data' => $user]);
                break;

            case 'ban_user':
                if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Invalid CSRF token');
                }

                $userId = $_POST['user_id'] ?? '';
                $banned = filter_var($_POST['banned'] ?? false, FILTER_VALIDATE_BOOLEAN);

                if (!$userId) {
                    throw new Exception('User ID required');
                }

                $result = banUser($userId, $banned);
                if ($result) {
                    DataProcessor::clearCache(); // Clear cache to reflect changes
                    logAdminAction('USER_BAN_STATUS_CHANGED', "User $userId " . ($banned ? 'banned' : 'unbanned'));
                    echo json_encode(['success' => true, 'message' => 'User status updated successfully']);
                } else {
                    throw new Exception('Failed to update user status');
                }
                break;

            default:
                throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get initial data for page load
$initialData = DataProcessor::getPaginatedUsers(1, DEFAULT_PAGE_SIZE);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ADMIN_PANEL_TITLE; ?> - User Management</title>

    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
        }

        body {
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .content-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: none;
        }

        .search-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .user-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .table th {
            background-color: #f8fafc;
            border: none;
            font-weight: 600;
            color: #374151;
            padding: 16px;
        }

        .table td {
            border: none;
            padding: 16px;
            vertical-align: middle;
        }

        .table tbody tr {
            border-bottom: 1px solid #e5e7eb;
            transition: background-color 0.2s;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-banned {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .btn-action {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            margin: 2px;
        }

        .pagination-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 20px;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            border-radius: 12px;
        }

        .filter-chip {
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            margin: 2px;
            display: inline-block;
        }

        .quick-stats {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">
                <i class="bi bi-speedometer2 me-2"></i>
                <?php echo ADMIN_PANEL_TITLE; ?>
            </a>

            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <a href="dashboard.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-house me-1"></i>
                    Dashboard
                </a>

                <a href="financial.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-graph-up me-1"></i>
                    Financial
                </a>

                <a href="analytics.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-trophy me-1"></i>
                    Analytics
                </a>

                <div class="dropdown">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        Admin
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="quickTotalUsers"><?php echo formatNumber($initialData['total']); ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="quickActiveUsers">-</div>
                        <div class="stat-label">Active Users</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="quickBannedUsers">-</div>
                        <div class="stat-label">Banned Users</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="quickNewUsers">-</div>
                        <div class="stat-label">New Today</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="search-container">
            <div class="row align-items-end">
                <div class="col-md-4">
                    <label for="searchInput" class="form-label fw-semibold">Search Users</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" class="form-control" id="searchInput"
                               placeholder="Name, username, or user ID">
                    </div>
                </div>

                <div class="col-md-2">
                    <label for="statusFilter" class="form-label fw-semibold">Status</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">All Users</option>
                        <option value="active">Active</option>
                        <option value="banned">Banned</option>
                        <option value="high_balance">High Balance</option>
                        <option value="pending_withdrawal">Pending Withdrawal</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="referralFilter" class="form-label fw-semibold">Referral Status</label>
                    <select class="form-select" id="referralFilter">
                        <option value="">All</option>
                        <option value="referred">Referred</option>
                        <option value="direct">Direct</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="pageSizeSelect" class="form-label fw-semibold">Page Size</label>
                    <select class="form-select" id="pageSizeSelect">
                        <option value="25">25</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <button class="btn btn-primary w-100" onclick="applyFilters()">
                        <i class="bi bi-funnel me-1"></i>
                        Apply Filters
                    </button>
                </div>
            </div>

            <!-- Active Filters Display -->
            <div id="activeFilters" class="mt-3" style="display: none;">
                <small class="text-muted me-2">Active filters:</small>
                <div id="filterChips" class="d-inline"></div>
                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="clearFilters()">
                    <i class="bi bi-x-circle me-1"></i>
                    Clear All
                </button>
            </div>
        </div>

        <!-- Users Table -->
        <div class="user-table position-relative">
            <div class="loading-overlay d-none" id="tableLoading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Balance</th>
                            <th>Referrals</th>
                            <th>Withdrawals</th>
                            <th>Status</th>
                            <th>Registration</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div id="paginationInfo" class="text-muted">
                        Showing 1-<?php echo min(DEFAULT_PAGE_SIZE, $initialData['total']); ?> of <?php echo $initialData['total']; ?> users
                    </div>
                </div>
                <div class="col-md-6">
                    <nav>
                        <ul class="pagination justify-content-end mb-0" id="pagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-circle me-2"></i>
                        User Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="userDetailsContent">
                    <!-- User details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-warning" id="banUserBtn" onclick="toggleUserBan()">
                        <i class="bi bi-ban me-1"></i>
                        Ban User
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentPage = 1;
        let currentPageSize = 50;
        let currentSearch = '';
        let currentFilters = {};
        let currentUserData = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            loadQuickStats();

            // Setup search input with debounce
            let searchTimeout;
            document.getElementById('searchInput').addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearch = this.value;
                    currentPage = 1;
                    loadUsers();
                }, 500);
            });

            // Setup filter change handlers
            document.getElementById('statusFilter').addEventListener('change', applyFilters);
            document.getElementById('referralFilter').addEventListener('change', applyFilters);
            document.getElementById('pageSizeSelect').addEventListener('change', function() {
                currentPageSize = parseInt(this.value);
                currentPage = 1;
                loadUsers();
            });
        });

        // Load users data
        function loadUsers() {
            showTableLoading(true);

            const params = new URLSearchParams({
                action: 'get_users',
                page: currentPage,
                pageSize: currentPageSize,
                search: currentSearch,
                status: currentFilters.status || '',
                referral_status: currentFilters.referral_status || ''
            });

            fetch('users.php?' + params)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderUsersTable(data.data.users);
                        renderPagination(data.data);
                        updateActiveFilters();
                    } else {
                        showError('Error loading users: ' + data.error);
                    }
                    showTableLoading(false);
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('Network error loading users');
                    showTableLoading(false);
                });
        }

        // Load quick statistics
        function loadQuickStats() {
            fetch('dashboard.php?action=get_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.data;
                        document.getElementById('quickActiveUsers').textContent = formatNumber(stats.active_users);
                        document.getElementById('quickBannedUsers').textContent = formatNumber(stats.banned_users);
                        document.getElementById('quickNewUsers').textContent = formatNumber(stats.new_users_today);
                    }
                })
                .catch(error => console.error('Error loading stats:', error));
        }

        // Render users table
        function renderUsersTable(users) {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            if (users.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">No users found matching your criteria</p>
                        </td>
                    </tr>
                `;
                return;
            }

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3">
                                ${(user.first_name || 'U').charAt(0).toUpperCase()}
                            </div>
                            <div>
                                <div class="fw-semibold">${escapeHtml(user.first_name || 'Unknown')}</div>
                                <small class="text-muted">
                                    ${user.username ? '@' + escapeHtml(user.username) : 'No username'}
                                </small>
                                <br>
                                <small class="text-muted">ID: ${user.user_id}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="fw-semibold">${formatCurrency(user.balance || 0)}</div>
                        ${user.withdraw_under_review > 0 ?
                            `<small class="text-warning">Pending: ${formatCurrency(user.withdraw_under_review)}</small>` :
                            ''}
                    </td>
                    <td>
                        <div class="fw-semibold">${(user.promotion_report || []).length}</div>
                        <small class="text-muted">
                            Earned: ${formatCurrency(calculateReferralEarnings(user.promotion_report || []))}
                        </small>
                    </td>
                    <td>
                        <div class="fw-semibold">${formatCurrency(user.successful_withdraw || 0)}</div>
                        <small class="text-muted">${(user.withdrawal_report || []).length} withdrawals</small>
                    </td>
                    <td>
                        <span class="status-badge ${user.banned ? 'status-banned' : 'status-active'}">
                            ${user.banned ? 'Banned' : 'Active'}
                        </span>
                        ${user.referred ? '<br><small class="text-info">Referred</small>' : ''}
                    </td>
                    <td>
                        <small class="text-muted">
                            ${formatDate(user.user_id)}
                        </small>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary btn-action"
                                onclick="showUserDetails(${user.user_id})">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-${user.banned ? 'success' : 'warning'} btn-action"
                                onclick="quickToggleBan(${user.user_id}, ${!user.banned})">
                            <i class="bi bi-${user.banned ? 'check-circle' : 'ban'}"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Render pagination
        function renderPagination(data) {
            const pagination = document.getElementById('pagination');
            const paginationInfo = document.getElementById('paginationInfo');

            // Update info
            const start = (data.page - 1) * data.pageSize + 1;
            const end = Math.min(data.page * data.pageSize, data.total);
            paginationInfo.textContent = `Showing ${start}-${end} of ${data.total} users`;

            // Clear pagination
            pagination.innerHTML = '';

            if (data.totalPages <= 1) return;

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${data.page === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${data.page - 1})">Previous</a>`;
            pagination.appendChild(prevLi);

            // Page numbers
            const startPage = Math.max(1, data.page - 2);
            const endPage = Math.min(data.totalPages, data.page + 2);

            if (startPage > 1) {
                const li = document.createElement('li');
                li.className = 'page-item';
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(1)">1</a>`;
                pagination.appendChild(li);

                if (startPage > 2) {
                    const li = document.createElement('li');
                    li.className = 'page-item disabled';
                    li.innerHTML = `<span class="page-link">...</span>`;
                    pagination.appendChild(li);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === data.page ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(li);
            }

            if (endPage < data.totalPages) {
                if (endPage < data.totalPages - 1) {
                    const li = document.createElement('li');
                    li.className = 'page-item disabled';
                    li.innerHTML = `<span class="page-link">...</span>`;
                    pagination.appendChild(li);
                }

                const li = document.createElement('li');
                li.className = 'page-item';
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${data.totalPages})">${data.totalPages}</a>`;
                pagination.appendChild(li);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${data.page === data.totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${data.page + 1})">Next</a>`;
            pagination.appendChild(nextLi);
        }

        // Change page
        function changePage(page) {
            if (page < 1) return;
            currentPage = page;
            loadUsers();
        }

        // Apply filters
        function applyFilters() {
            currentFilters = {
                status: document.getElementById('statusFilter').value,
                referral_status: document.getElementById('referralFilter').value
            };
            currentPage = 1;
            loadUsers();
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('referralFilter').value = '';

            currentSearch = '';
            currentFilters = {};
            currentPage = 1;

            loadUsers();
        }

        // Update active filters display
        function updateActiveFilters() {
            const activeFiltersDiv = document.getElementById('activeFilters');
            const filterChips = document.getElementById('filterChips');

            let hasFilters = false;
            filterChips.innerHTML = '';

            if (currentSearch) {
                filterChips.innerHTML += `<span class="filter-chip">Search: "${currentSearch}"</span>`;
                hasFilters = true;
            }

            if (currentFilters.status) {
                filterChips.innerHTML += `<span class="filter-chip">Status: ${currentFilters.status}</span>`;
                hasFilters = true;
            }

            if (currentFilters.referral_status) {
                filterChips.innerHTML += `<span class="filter-chip">Referral: ${currentFilters.referral_status}</span>`;
                hasFilters = true;
            }

            activeFiltersDiv.style.display = hasFilters ? 'block' : 'none';
        }

        // Show user details modal
        function showUserDetails(userId) {
            const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
            const content = document.getElementById('userDetailsContent');

            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;

            modal.show();

            fetch(`users.php?action=get_user_details&user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentUserData = data.data;
                        renderUserDetails(data.data);
                        updateBanButton(data.data.banned);
                    } else {
                        content.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    content.innerHTML = `<div class="alert alert-danger">Network error loading user details</div>`;
                });
        }

        // Render user details
        function renderUserDetails(user) {
            const content = document.getElementById('userDetailsContent');

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Basic Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td class="fw-semibold">User ID:</td>
                                <td>${user.user_id}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Name:</td>
                                <td>${escapeHtml(user.first_name || 'Unknown')}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Username:</td>
                                <td>${user.username ? '@' + escapeHtml(user.username) : 'Not set'}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Status:</td>
                                <td>
                                    <span class="status-badge ${user.banned ? 'status-banned' : 'status-active'}">
                                        ${user.banned ? 'Banned' : 'Active'}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Referred:</td>
                                <td>${user.referred ? 'Yes' : 'No'}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Referred By:</td>
                                <td>${user.referred_by !== 'None' ? user.referred_by : 'Direct signup'}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Financial Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td class="fw-semibold">Current Balance:</td>
                                <td class="text-success fw-bold">${formatCurrency(user.balance || 0)}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Total Withdrawn:</td>
                                <td class="text-info">${formatCurrency(user.successful_withdraw || 0)}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Pending Withdrawal:</td>
                                <td class="text-warning">${formatCurrency(user.withdraw_under_review || 0)}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Joining Bonus:</td>
                                <td>${formatCurrency(user.joining_bonus_got || 0)}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Referral Earnings:</td>
                                <td class="text-success">${formatCurrency(user.total_referral_earnings || 0)}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Referral Activity</h6>
                        <div class="card">
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="h4 text-primary mb-1">${user.referral_count || 0}</div>
                                        <small class="text-muted">Total Referrals</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="h4 text-success mb-1">${formatCurrency(user.total_referral_earnings || 0)}</div>
                                        <small class="text-muted">Earnings</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${user.promotion_report && user.promotion_report.length > 0 ? `
                            <div class="mt-3">
                                <small class="text-muted">Recent Referrals:</small>
                                <div class="mt-2" style="max-height: 200px; overflow-y: auto;">
                                    ${user.promotion_report.slice(-5).reverse().map(report => `
                                        <div class="d-flex justify-content-between align-items-center py-1 border-bottom">
                                            <small>${escapeHtml(report.referred_user_name || 'Unknown')}</small>
                                            <small class="text-success">${formatCurrency(report.amount_got || 0)}</small>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>

                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Account Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td class="fw-semibold">Account Name:</td>
                                <td>${escapeHtml(user.account_info?.name || 'Not set')}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Mobile:</td>
                                <td>${escapeHtml(user.account_info?.mobile_number || 'Not set')}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Email:</td>
                                <td>${escapeHtml(user.account_info?.email || 'Not set')}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Withdrawal Method:</td>
                                <td>${user.account_info?.withdrawal_method === 'usdt' ? 'USDT/Binance' : 'Bank Transfer'}</td>
                            </tr>
                            ${user.account_info?.withdrawal_method === 'bank' ? `
                                <tr>
                                    <td class="fw-semibold">Account Number:</td>
                                    <td>${escapeHtml(user.account_info?.account_number || 'Not set')}</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">IFSC Code:</td>
                                    <td>${escapeHtml(user.account_info?.ifsc || 'Not set')}</td>
                                </tr>
                            ` : `
                                <tr>
                                    <td class="fw-semibold">Binance ID:</td>
                                    <td>${escapeHtml(user.account_info?.usdt_address || 'Not set')}</td>
                                </tr>
                            `}
                        </table>
                    </div>
                </div>
            `;
        }

        // Update ban button
        function updateBanButton(isBanned) {
            const banBtn = document.getElementById('banUserBtn');
            if (isBanned) {
                banBtn.className = 'btn btn-success';
                banBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>Unban User';
            } else {
                banBtn.className = 'btn btn-warning';
                banBtn.innerHTML = '<i class="bi bi-ban me-1"></i>Ban User';
            }
        }

        // Toggle user ban status
        function toggleUserBan() {
            if (!currentUserData) return;

            const newBanStatus = !currentUserData.banned;
            const action = newBanStatus ? 'ban' : 'unban';

            if (!confirm(`Are you sure you want to ${action} this user?`)) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'ban_user');
            formData.append('user_id', currentUserData.user_id);
            formData.append('banned', newBanStatus);
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUserData.banned = newBanStatus;
                    updateBanButton(newBanStatus);
                    loadUsers(); // Refresh the table
                    showSuccess(`User ${action}ned successfully`);
                } else {
                    showError('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Network error');
            });
        }

        // Quick toggle ban from table
        function quickToggleBan(userId, newBanStatus) {
            const action = newBanStatus ? 'ban' : 'unban';

            if (!confirm(`Are you sure you want to ${action} this user?`)) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'ban_user');
            formData.append('user_id', userId);
            formData.append('banned', newBanStatus);
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadUsers(); // Refresh the table
                    showSuccess(`User ${action}ned successfully`);
                } else {
                    showError('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Network error');
            });
        }

        // Utility functions
        function showTableLoading(show) {
            const loading = document.getElementById('tableLoading');
            loading.classList.toggle('d-none', !show);
        }

        function showError(message) {
            // You can implement a toast notification system here
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            // You can implement a toast notification system here
            alert('Success: ' + message);
        }

        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toLocaleString();
        }

        function formatCurrency(amount) {
            return '₹' + parseFloat(amount || 0).toLocaleString('en-IN', {minimumFractionDigits: 2});
        }

        function formatDate(timestamp) {
            // Simple date formatting from user ID (timestamp)
            try {
                const date = new Date(parseInt(timestamp) * 1000);
                return date.toLocaleDateString('en-IN');
            } catch (e) {
                return 'Unknown';
            }
        }

        function calculateReferralEarnings(promotionReports) {
            return promotionReports.reduce((total, report) => total + (report.amount_got || 0), 0);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('auth.php?action=logout')
                    .then(() => {
                        window.location.href = 'login.php';
                    });
            }
        }
    </script>
</body>
</html>