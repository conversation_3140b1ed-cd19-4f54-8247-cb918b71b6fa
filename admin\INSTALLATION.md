# InstantoPay Bot Admin Panel - Installation Guide

Complete installation and setup guide for the InstantoPay Bot Admin Panel, optimized for 100,000+ users on shared hosting.

## 📋 Prerequisites

### System Requirements
- **PHP Version:** 7.4 or higher (8.0+ recommended)
- **Web Server:** Apache or Nginx
- **Storage:** Minimum 500MB free space
- **Memory:** 128MB PHP memory limit (256MB recommended)
- **Extensions:** JSON, Session, cURL (standard PHP extensions)

### Hosting Requirements
- **Shared Hosting Compatible:** Yes (tested on Hostinger)
- **SSL Certificate:** Recommended for security
- **File Permissions:** Write access to admin directory
- **Database:** MySQL 5.7+ (if using database mode)

## 🚀 Quick Installation

### Step 1: Download and Upload Files

1. **Download the admin panel files**
2. **Upload to your server:**
   ```bash
   # Upload the entire 'admin' folder to your bot directory
   # Your structure should look like:
   /your-bot-directory/
   ├── admin/           # Admin panel files
   ├── config.php       # Your existing bot config
   ├── data/           # Your existing bot data
   └── ...             # Other bot files
   ```

### Step 2: Set File Permissions

```bash
# Set directory permissions
chmod 755 admin/
chmod 777 admin/cache/
chmod 777 admin/logs/

# Set file permissions
chmod 644 admin/*.php
chmod 644 admin/.htaccess
```

### Step 3: Access the Admin Panel

1. **Open your browser and navigate to:**
   ```
   https://yourdomain.com/admin/
   ```

2. **Login with the default passcode:**
   ```
   Passcode: 1412
   ```

3. **You should see the dashboard with your bot statistics!**

## 🔧 Detailed Configuration

### Security Configuration

1. **Change the default passcode** in `admin/config.php`:
   ```php
   define('ADMIN_PASSCODE', 'your-new-secure-passcode');
   ```

2. **Configure session timeout:**
   ```php
   define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour
   ```

3. **Set up rate limiting:**
   ```php
   define('MAX_LOGIN_ATTEMPTS', 5);
   define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes
   ```

### Performance Configuration

1. **Adjust cache settings** for your server:
   ```php
   define('CACHE_DURATION', 300);        // 5 minutes
   define('STATS_CACHE_DURATION', 60);   // 1 minute
   define('DEFAULT_PAGE_SIZE', 50);      // Users per page
   ```

2. **Configure memory limits** in `admin/config.php`:
   ```php
   ini_set('memory_limit', '256M');      // Increase if needed
   ini_set('max_execution_time', 60);    // Increase for large datasets
   ```

### Database Optimization (If Using MySQL)

1. **Apply database indexes** from `admin/database_optimization.sql`:
   ```sql
   -- Run these commands in your MySQL database
   CREATE INDEX idx_username ON users (username);
   CREATE INDEX idx_banned ON users (banned);
   CREATE INDEX idx_balance ON users (balance);
   ```

2. **Configure MySQL settings** for large datasets:
   ```ini
   [mysqld]
   innodb_buffer_pool_size = 1G
   query_cache_size = 128M
   max_connections = 200
   ```

## 🛡️ Security Hardening

### 1. SSL/HTTPS Setup

**For cPanel/Shared Hosting:**
1. Enable SSL in your hosting control panel
2. Force HTTPS redirects in `.htaccess`
3. Update your bot webhook URL to use HTTPS

**For VPS/Dedicated Servers:**
```bash
# Install Let's Encrypt SSL
certbot --apache -d yourdomain.com
```

### 2. File Protection

The included `.htaccess` file provides:
- Protection of sensitive PHP files
- Security headers
- Request filtering
- Cache optimization

### 3. Additional Security Measures

1. **Rename the admin directory:**
   ```bash
   mv admin/ secure-admin-panel/
   ```

2. **IP Restriction** (optional) - Add to `.htaccess`:
   ```apache
   <RequireAll>
       Require ip *************
       Require ip ***********/24
   </RequireAll>
   ```

3. **Two-Factor Authentication** (advanced):
   - Implement Google Authenticator
   - Use hardware security keys

## 📊 Performance Optimization

### For Large Datasets (100k+ Users)

1. **Enable PHP OPcache:**
   ```ini
   opcache.enable=1
   opcache.memory_consumption=128
   opcache.max_accelerated_files=4000
   ```

2. **Optimize JSON file handling:**
   ```php
   // In config.php, increase limits for large files
   ini_set('memory_limit', '512M');
   ini_set('max_execution_time', 120);
   ```

3. **Implement file-based caching:**
   ```bash
   # Ensure cache directory is writable
   chmod 777 admin/cache/
   
   # Set up cache cleanup cron job
   0 */6 * * * find /path/to/admin/cache/ -name "*.json" -mtime +1 -delete
   ```

### Shared Hosting Optimization

1. **Hostinger-specific settings:**
   ```php
   // Optimize for Hostinger shared hosting
   ini_set('max_execution_time', 30);
   ini_set('memory_limit', '128M');
   define('DEFAULT_PAGE_SIZE', 25); // Smaller page size
   ```

2. **CloudFlare integration:**
   - Enable CloudFlare caching
   - Set up page rules for admin panel
   - Configure security settings

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. "Permission Denied" Errors
```bash
# Fix file permissions
chmod -R 755 admin/
chmod -R 777 admin/cache/
chmod -R 777 admin/logs/
```

#### 2. "Memory Limit Exceeded"
```php
// Increase memory limit in config.php
ini_set('memory_limit', '256M');

// Or reduce page size
define('DEFAULT_PAGE_SIZE', 25);
```

#### 3. "Session Timeout" Issues
```php
// Increase session timeout
define('ADMIN_SESSION_TIMEOUT', 7200); // 2 hours

// Check session configuration
ini_set('session.gc_maxlifetime', 7200);
```

#### 4. "Cache Directory Not Writable"
```bash
# Create and set permissions
mkdir admin/cache/
chmod 777 admin/cache/

# Check ownership
chown www-data:www-data admin/cache/
```

#### 5. "Database Connection Failed"
```php
// Check database configuration in main config.php
// Ensure database credentials are correct
// Test connection manually
```

### Performance Issues

#### Slow Loading Times
1. **Enable caching:**
   ```php
   define('CACHE_DURATION', 600); // 10 minutes
   ```

2. **Reduce data processing:**
   ```php
   define('DEFAULT_PAGE_SIZE', 25);
   define('MAX_SEARCH_RESULTS', 500);
   ```

3. **Optimize queries:**
   - Add database indexes
   - Use pagination
   - Implement lazy loading

#### High Memory Usage
1. **Stream large files:**
   ```php
   // Enable streaming for files > 50MB
   define('STREAM_LARGE_FILES', true);
   ```

2. **Clear cache regularly:**
   ```bash
   # Add to crontab
   0 */4 * * * rm -rf /path/to/admin/cache/*
   ```

## 📱 Mobile Optimization

### Responsive Design Testing
1. Test on various screen sizes
2. Verify touch interactions
3. Check loading times on mobile networks

### Mobile-Specific Optimizations
```css
/* Already included in the admin panel */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    .pagination {
        justify-content: center;
    }
}
```

## 🔄 Updates and Maintenance

### Regular Maintenance Tasks

1. **Weekly:**
   - Clear cache files
   - Check error logs
   - Review security logs

2. **Monthly:**
   - Update passcode
   - Review user access
   - Backup configuration

3. **Quarterly:**
   - Update PHP version
   - Review security settings
   - Performance optimization

### Backup Strategy

1. **Configuration backup:**
   ```bash
   cp admin/config.php admin/config.php.backup
   ```

2. **Full admin panel backup:**
   ```bash
   tar -czf admin-panel-backup-$(date +%Y%m%d).tar.gz admin/
   ```

3. **Database backup (if using MySQL):**
   ```bash
   mysqldump -u username -p database_name > backup.sql
   ```

## 📞 Support and Help

### Getting Help

1. **Check the logs:**
   ```bash
   tail -f admin/logs/admin_errors.log
   tail -f admin/logs/admin_actions.log
   ```

2. **Enable debug mode:**
   ```php
   // In config.php
   error_reporting(E_ALL);
   ini_set('display_errors', 1);
   ```

3. **Common solutions:**
   - Clear browser cache
   - Check file permissions
   - Verify PHP version
   - Review error logs

### Performance Monitoring

1. **Monitor response times**
2. **Check memory usage**
3. **Review cache hit rates**
4. **Monitor disk space**

### Security Monitoring

1. **Review login attempts**
2. **Check for suspicious activity**
3. **Monitor file changes**
4. **Review access logs**

---

## ✅ Installation Checklist

- [ ] PHP 7.4+ installed and configured
- [ ] Admin files uploaded to server
- [ ] File permissions set correctly
- [ ] Admin panel accessible via browser
- [ ] Default passcode changed
- [ ] SSL/HTTPS enabled
- [ ] Cache directory writable
- [ ] Error logging enabled
- [ ] Performance optimizations applied
- [ ] Security measures implemented
- [ ] Backup strategy in place
- [ ] Monitoring configured

**Congratulations! Your InstantoPay Bot Admin Panel is now ready for production use with 100,000+ users!**
