<?php
/**
 * Admin Panel Configuration
 * Secure configuration for the Telegram Bot Admin Panel
 */

// Prevent direct access
if (!defined('ADMIN_PANEL_ACCESS')) {
    die('Direct access not allowed');
}

// Define admin panel access constant
define('ADMIN_PANEL_ACCESS', true);

// Include main bot configuration
require_once __DIR__ . '/../config.php';

// ========================================
// ADMIN PANEL CONFIGURATION
// ========================================

// Admin Panel Settings
define('ADMIN_PANEL_TITLE', 'InstantoPay Bot Admin Panel');
define('ADMIN_PANEL_VERSION', '1.0.0');
define('ADMIN_PASSCODE', '1412'); // Secure passcode for admin access

// Session Configuration
define('ADMIN_SESSION_NAME', 'instanto_admin_session');
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('ADMIN_SESSION_REGENERATE_INTERVAL', 300); // 5 minutes

// Security Settings
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes
define('CSRF_TOKEN_LIFETIME', 1800); // 30 minutes

// Performance Settings for Large Datasets
define('DEFAULT_PAGE_SIZE', 50);
define('MAX_PAGE_SIZE', 200);
define('CACHE_DURATION', 300); // 5 minutes
define('STATS_CACHE_DURATION', 60); // 1 minute for real-time stats

// File Paths
define('ADMIN_DIR', __DIR__);
define('ADMIN_CACHE_DIR', ADMIN_DIR . '/cache');
define('ADMIN_LOGS_DIR', ADMIN_DIR . '/logs');
define('ADMIN_TEMPLATES_DIR', ADMIN_DIR . '/templates');

// Database Query Limits for Performance
define('MAX_SEARCH_RESULTS', 1000);
define('BULK_OPERATION_LIMIT', 100);

// Chart and Analytics Settings
define('CHART_DATA_POINTS', 30); // Last 30 days for charts
define('LEADERBOARD_LIMIT', 50);

// Create necessary directories
$directories = [ADMIN_CACHE_DIR, ADMIN_LOGS_DIR, ADMIN_TEMPLATES_DIR];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Error reporting for admin panel (can be more verbose than main bot)
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to users
ini_set('log_errors', 1);
ini_set('error_log', ADMIN_LOGS_DIR . '/admin_errors.log');

// Set timezone
date_default_timezone_set('Asia/Kolkata');

// Security headers
function setSecurityHeaders() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net; style-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net; img-src \'self\' data:;');
}

// Initialize security headers
setSecurityHeaders();

// Start secure session
function startSecureSession() {
    // Configure session security
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    session_name(ADMIN_SESSION_NAME);
    session_start();
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > ADMIN_SESSION_REGENERATE_INTERVAL) {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

// CSRF Protection
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || 
        (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_LIFETIME) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && 
           isset($_SESSION['csrf_token_time']) &&
           (time() - $_SESSION['csrf_token_time']) <= CSRF_TOKEN_LIFETIME &&
           hash_equals($_SESSION['csrf_token'], $token);
}

// Rate limiting for login attempts
function checkLoginAttempts($ip) {
    $attempts_file = ADMIN_CACHE_DIR . '/login_attempts_' . md5($ip) . '.json';
    
    if (!file_exists($attempts_file)) {
        return true;
    }
    
    $attempts = json_decode(file_get_contents($attempts_file), true);
    
    // Clean old attempts
    $attempts = array_filter($attempts, function($time) {
        return (time() - $time) < LOGIN_LOCKOUT_TIME;
    });
    
    file_put_contents($attempts_file, json_encode($attempts));
    
    return count($attempts) < MAX_LOGIN_ATTEMPTS;
}

function recordLoginAttempt($ip) {
    $attempts_file = ADMIN_CACHE_DIR . '/login_attempts_' . md5($ip) . '.json';
    
    $attempts = [];
    if (file_exists($attempts_file)) {
        $attempts = json_decode(file_get_contents($attempts_file), true) ?: [];
    }
    
    $attempts[] = time();
    file_put_contents($attempts_file, json_encode($attempts));
}

// Logging function
function logAdminAction($action, $details = '') {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'action' => $action,
        'details' => $details,
        'session_id' => session_id()
    ];
    
    $log_line = json_encode($log_entry) . "\n";
    file_put_contents(ADMIN_LOGS_DIR . '/admin_actions.log', $log_line, FILE_APPEND | LOCK_EX);
}

// Utility functions
function formatNumber($number) {
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

function timeAgo($timestamp) {
    $time = time() - $timestamp;
    
    if ($time < 60) return 'Just now';
    if ($time < 3600) return floor($time / 60) . ' minutes ago';
    if ($time < 86400) return floor($time / 3600) . ' hours ago';
    if ($time < 2592000) return floor($time / 86400) . ' days ago';
    if ($time < 31536000) return floor($time / 2592000) . ' months ago';
    return floor($time / 31536000) . ' years ago';
}

// Initialize session
startSecureSession();
