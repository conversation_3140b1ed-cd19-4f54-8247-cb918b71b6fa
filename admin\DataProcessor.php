<?php
/**
 * Data Processing Layer for Admin Panel
 * Optimized for handling 100k+ users efficiently
 */

define('ADMIN_PANEL_ACCESS', true);
require_once 'config.php';

class DataProcessor {
    
    private static $cache = [];
    private static $cacheFile = null;
    
    public function __construct() {
        self::$cacheFile = ADMIN_CACHE_DIR . '/data_cache.json';
    }
    
    /**
     * Get cached data or compute if expired
     */
    private static function getCachedData($key, $callback, $duration = CACHE_DURATION) {
        $cacheData = self::loadCache();
        
        if (isset($cacheData[$key]) && 
            (time() - $cacheData[$key]['timestamp']) < $duration) {
            return $cacheData[$key]['data'];
        }
        
        // Compute fresh data
        $data = $callback();
        
        // Cache the result
        $cacheData[$key] = [
            'data' => $data,
            'timestamp' => time()
        ];
        
        self::saveCache($cacheData);
        return $data;
    }
    
    /**
     * Load cache from file
     */
    private static function loadCache() {
        if (!file_exists(self::$cacheFile)) {
            return [];
        }
        
        $content = file_get_contents(self::$cacheFile);
        return json_decode($content, true) ?: [];
    }
    
    /**
     * Save cache to file
     */
    private static function saveCache($data) {
        file_put_contents(self::$cacheFile, json_encode($data), LOCK_EX);
    }
    
    /**
     * Clear specific cache key or all cache
     */
    public static function clearCache($key = null) {
        if ($key === null) {
            if (file_exists(self::$cacheFile)) {
                unlink(self::$cacheFile);
            }
        } else {
            $cacheData = self::loadCache();
            unset($cacheData[$key]);
            self::saveCache($cacheData);
        }
    }
    
    /**
     * Get all users data efficiently
     */
    public static function getAllUsers() {
        if (STORAGE_MODE === 'json') {
            return self::getAllUsersJson();
        } else {
            return self::getAllUsersMysql();
        }
    }
    
    /**
     * Get users from JSON file with memory optimization
     */
    private static function getAllUsersJson() {
        return self::getCachedData('all_users_json', function() {
            $usersFile = USERS_FILE;
            if (!file_exists($usersFile)) {
                return [];
            }
            
            // For very large files, use streaming approach
            $fileSize = filesize($usersFile);
            if ($fileSize > 50 * 1024 * 1024) { // 50MB
                return self::streamJsonUsers($usersFile);
            }
            
            $content = file_get_contents($usersFile);
            return json_decode($content, true) ?: [];
        }, CACHE_DURATION);
    }
    
    /**
     * Stream large JSON files to avoid memory issues
     */
    private static function streamJsonUsers($file) {
        $users = [];
        $handle = fopen($file, 'r');
        
        if (!$handle) {
            return [];
        }
        
        $buffer = '';
        $inUser = false;
        $userBuffer = '';
        $userCount = 0;
        
        while (($line = fgets($handle)) !== false) {
            $buffer .= $line;
            
            // Simple JSON parsing for user objects
            if (strpos($line, '"user_id":') !== false && !$inUser) {
                $inUser = true;
                $userBuffer = '';
            }
            
            if ($inUser) {
                $userBuffer .= $line;
                
                // Check for end of user object
                if (strpos($line, '},') !== false || strpos($line, '}') !== false) {
                    $inUser = false;
                    
                    // Try to parse this user
                    $userJson = trim($userBuffer, " \t\n\r\0\x0B,");
                    if (substr($userJson, -1) !== '}') {
                        $userJson .= '}';
                    }
                    
                    $userData = json_decode('{' . $userJson, true);
                    if ($userData && isset($userData['user_id'])) {
                        $users[$userData['user_id']] = $userData;
                        $userCount++;
                        
                        // Limit memory usage for very large datasets
                        if ($userCount > 50000) {
                            break;
                        }
                    }
                }
            }
        }
        
        fclose($handle);
        return $users;
    }
    
    /**
     * Get users from MySQL with optimization
     */
    private static function getAllUsersMysql() {
        return self::getCachedData('all_users_mysql', function() {
            require_once '../database_functions.php';
            return getAllUsers();
        }, CACHE_DURATION);
    }
    
    /**
     * Get paginated users with search and filters
     */
    public static function getPaginatedUsers($page = 1, $pageSize = DEFAULT_PAGE_SIZE, $search = '', $filters = []) {
        $pageSize = min($pageSize, MAX_PAGE_SIZE);
        $offset = ($page - 1) * $pageSize;
        
        $cacheKey = 'paginated_users_' . md5(serialize([$page, $pageSize, $search, $filters]));
        
        return self::getCachedData($cacheKey, function() use ($offset, $pageSize, $search, $filters) {
            $allUsers = self::getAllUsers();
            
            // Apply filters and search
            $filteredUsers = self::filterUsers($allUsers, $search, $filters);
            
            // Sort users (newest first by default)
            uasort($filteredUsers, function($a, $b) {
                return ($b['user_id'] ?? 0) - ($a['user_id'] ?? 0);
            });
            
            $total = count($filteredUsers);
            $users = array_slice($filteredUsers, $offset, $pageSize, true);
            
            return [
                'users' => $users,
                'total' => $total,
                'page' => $offset / $pageSize + 1,
                'pageSize' => $pageSize,
                'totalPages' => ceil($total / $pageSize)
            ];
        }, 60); // Shorter cache for paginated data
    }
    
    /**
     * Filter users based on search and criteria
     */
    private static function filterUsers($users, $search = '', $filters = []) {
        if (empty($search) && empty($filters)) {
            return $users;
        }
        
        return array_filter($users, function($user) use ($search, $filters) {
            // Search filter
            if (!empty($search)) {
                $searchLower = strtolower($search);
                $searchFields = [
                    strtolower($user['first_name'] ?? ''),
                    strtolower($user['username'] ?? ''),
                    (string)($user['user_id'] ?? '')
                ];
                
                $found = false;
                foreach ($searchFields as $field) {
                    if (strpos($field, $searchLower) !== false) {
                        $found = true;
                        break;
                    }
                }
                
                if (!$found) {
                    return false;
                }
            }
            
            // Status filters
            if (isset($filters['status'])) {
                switch ($filters['status']) {
                    case 'banned':
                        if (!($user['banned'] ?? false)) return false;
                        break;
                    case 'active':
                        if ($user['banned'] ?? false) return false;
                        break;
                    case 'high_balance':
                        if (($user['balance'] ?? 0) < 1000) return false;
                        break;
                    case 'pending_withdrawal':
                        if (($user['withdraw_under_review'] ?? 0) <= 0) return false;
                        break;
                }
            }
            
            // Referral status filter
            if (isset($filters['referral_status'])) {
                switch ($filters['referral_status']) {
                    case 'referred':
                        if (!($user['referred'] ?? false)) return false;
                        break;
                    case 'direct':
                        if ($user['referred'] ?? false) return false;
                        break;
                }
            }
            
            return true;
        });
    }
    
    /**
     * Get user statistics optimized for large datasets
     */
    public static function getUserStatistics() {
        return self::getCachedData('user_statistics', function() {
            $users = self::getAllUsers();
            
            $stats = [
                'total_users' => 0,
                'active_users' => 0,
                'banned_users' => 0,
                'referred_users' => 0,
                'total_balance' => 0,
                'total_withdrawals' => 0,
                'pending_withdrawals' => 0,
                'total_referrals' => 0,
                'new_users_today' => 0,
                'active_users_7d' => 0,
                'active_users_30d' => 0
            ];
            
            $today = date('Y-m-d');
            $sevenDaysAgo = strtotime('-7 days');
            $thirtyDaysAgo = strtotime('-30 days');
            
            foreach ($users as $user) {
                $stats['total_users']++;
                
                if ($user['banned'] ?? false) {
                    $stats['banned_users']++;
                } else {
                    $stats['active_users']++;
                }
                
                if ($user['referred'] ?? false) {
                    $stats['referred_users']++;
                }
                
                $stats['total_balance'] += $user['balance'] ?? 0;
                $stats['total_withdrawals'] += $user['successful_withdraw'] ?? 0;
                $stats['pending_withdrawals'] += $user['withdraw_under_review'] ?? 0;
                
                // Count referrals
                $promotionReports = $user['promotion_report'] ?? [];
                $stats['total_referrals'] += count($promotionReports);
                
                // Check if user registered today
                $userId = $user['user_id'] ?? 0;
                if ($userId > 0) {
                    $userDate = date('Y-m-d', $userId);
                    if ($userDate === $today) {
                        $stats['new_users_today']++;
                    }
                }
                
                // Note: For activity tracking, we'd need last_activity field
                // For now, we'll estimate based on recent referrals or withdrawals
                $hasRecentActivity = false;
                foreach ($promotionReports as $report) {
                    if (isset($report['timestamp']) && $report['timestamp'] > $sevenDaysAgo) {
                        $hasRecentActivity = true;
                        break;
                    }
                }
                
                if ($hasRecentActivity) {
                    $stats['active_users_7d']++;
                    $stats['active_users_30d']++;
                }
            }
            
            return $stats;
        }, STATS_CACHE_DURATION);
    }

    /**
     * Get leaderboard data
     */
    public static function getLeaderboards() {
        return self::getCachedData('leaderboards', function() {
            $users = self::getAllUsers();

            $topReferrers = [];
            $topWithdrawers = [];
            $topBalances = [];

            foreach ($users as $user) {
                $userId = $user['user_id'] ?? 0;
                $firstName = $user['first_name'] ?? 'Unknown';
                $username = $user['username'] ?? '';

                // Top Referrers
                $referralCount = count($user['promotion_report'] ?? []);
                if ($referralCount > 0) {
                    $topReferrers[] = [
                        'user_id' => $userId,
                        'name' => $firstName,
                        'username' => $username,
                        'referrals' => $referralCount,
                        'earnings' => array_sum(array_column($user['promotion_report'] ?? [], 'amount_got'))
                    ];
                }

                // Top Withdrawers
                $totalWithdrawn = $user['successful_withdraw'] ?? 0;
                if ($totalWithdrawn > 0) {
                    $topWithdrawers[] = [
                        'user_id' => $userId,
                        'name' => $firstName,
                        'username' => $username,
                        'amount' => $totalWithdrawn,
                        'withdrawal_count' => count($user['withdrawal_report'] ?? [])
                    ];
                }

                // Top Balances
                $balance = $user['balance'] ?? 0;
                if ($balance > 0) {
                    $topBalances[] = [
                        'user_id' => $userId,
                        'name' => $firstName,
                        'username' => $username,
                        'balance' => $balance
                    ];
                }
            }

            // Sort and limit
            usort($topReferrers, function($a, $b) { return $b['referrals'] - $a['referrals']; });
            usort($topWithdrawers, function($a, $b) { return $b['amount'] - $a['amount']; });
            usort($topBalances, function($a, $b) { return $b['balance'] - $a['balance']; });

            return [
                'top_referrers' => array_slice($topReferrers, 0, LEADERBOARD_LIMIT),
                'top_withdrawers' => array_slice($topWithdrawers, 0, LEADERBOARD_LIMIT),
                'top_balances' => array_slice($topBalances, 0, LEADERBOARD_LIMIT)
            ];
        }, CACHE_DURATION);
    }

    /**
     * Get financial analytics
     */
    public static function getFinancialAnalytics() {
        return self::getCachedData('financial_analytics', function() {
            $users = self::getAllUsers();

            $analytics = [
                'total_balance' => 0,
                'total_withdrawals' => 0,
                'pending_withdrawals' => 0,
                'total_earnings_paid' => 0,
                'average_balance' => 0,
                'withdrawal_success_rate' => 0,
                'commission_earned' => 0,
                'balance_distribution' => [
                    '0-100' => 0,
                    '100-500' => 0,
                    '500-1000' => 0,
                    '1000-5000' => 0,
                    '5000+' => 0
                ]
            ];

            $totalUsers = 0;
            $usersWithWithdrawals = 0;

            foreach ($users as $user) {
                $balance = $user['balance'] ?? 0;
                $withdrawn = $user['successful_withdraw'] ?? 0;
                $pending = $user['withdraw_under_review'] ?? 0;

                $analytics['total_balance'] += $balance;
                $analytics['total_withdrawals'] += $withdrawn;
                $analytics['pending_withdrawals'] += $pending;

                // Calculate earnings paid (referral rewards)
                $promotionReports = $user['promotion_report'] ?? [];
                foreach ($promotionReports as $report) {
                    $analytics['total_earnings_paid'] += $report['amount_got'] ?? 0;
                }

                // Balance distribution
                if ($balance == 0) {
                    $analytics['balance_distribution']['0-100']++;
                } elseif ($balance <= 100) {
                    $analytics['balance_distribution']['0-100']++;
                } elseif ($balance <= 500) {
                    $analytics['balance_distribution']['100-500']++;
                } elseif ($balance <= 1000) {
                    $analytics['balance_distribution']['500-1000']++;
                } elseif ($balance <= 5000) {
                    $analytics['balance_distribution']['1000-5000']++;
                } else {
                    $analytics['balance_distribution']['5000+']++;
                }

                $totalUsers++;
                if ($withdrawn > 0) {
                    $usersWithWithdrawals++;
                }
            }

            // Calculate averages and rates
            if ($totalUsers > 0) {
                $analytics['average_balance'] = $analytics['total_balance'] / $totalUsers;
                $analytics['withdrawal_success_rate'] = ($usersWithWithdrawals / $totalUsers) * 100;
            }

            // Estimate commission (10% of withdrawals as per admin settings)
            $analytics['commission_earned'] = $analytics['total_withdrawals'] * 0.10;

            return $analytics;
        }, CACHE_DURATION);
    }

    /**
     * Get chart data for dashboard
     */
    public static function getChartData() {
        return self::getCachedData('chart_data', function() {
            $users = self::getAllUsers();

            // User registration trend (last 30 days)
            $registrationTrend = [];
            $activityDistribution = [
                'Active' => 0,
                'Banned' => 0,
                'Inactive' => 0
            ];

            $financialOverview = [
                'Total Balance' => 0,
                'Successful Withdrawals' => 0,
                'Pending Withdrawals' => 0
            ];

            // Initialize last 30 days
            for ($i = 29; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-$i days"));
                $registrationTrend[$date] = 0;
            }

            foreach ($users as $user) {
                // Registration trend (estimate from user ID)
                $userId = $user['user_id'] ?? 0;
                if ($userId > 0) {
                    $userDate = date('Y-m-d', $userId);
                    if (isset($registrationTrend[$userDate])) {
                        $registrationTrend[$userDate]++;
                    }
                }

                // Activity distribution
                if ($user['banned'] ?? false) {
                    $activityDistribution['Banned']++;
                } else {
                    // Simple activity check based on balance or recent referrals
                    $hasActivity = ($user['balance'] ?? 0) > 0 ||
                                  count($user['promotion_report'] ?? []) > 0;

                    if ($hasActivity) {
                        $activityDistribution['Active']++;
                    } else {
                        $activityDistribution['Inactive']++;
                    }
                }

                // Financial overview
                $financialOverview['Total Balance'] += $user['balance'] ?? 0;
                $financialOverview['Successful Withdrawals'] += $user['successful_withdraw'] ?? 0;
                $financialOverview['Pending Withdrawals'] += $user['withdraw_under_review'] ?? 0;
            }

            return [
                'registration_trend' => $registrationTrend,
                'activity_distribution' => $activityDistribution,
                'financial_overview' => $financialOverview
            ];
        }, CACHE_DURATION);
    }

    /**
     * Search users efficiently
     */
    public static function searchUsers($query, $limit = 100) {
        $cacheKey = 'search_' . md5($query . $limit);

        return self::getCachedData($cacheKey, function() use ($query, $limit) {
            $users = self::getAllUsers();
            $results = [];
            $queryLower = strtolower(trim($query));

            if (empty($queryLower)) {
                return [];
            }

            foreach ($users as $user) {
                if (count($results) >= $limit) {
                    break;
                }

                $matches = false;

                // Check user ID (exact match)
                if ((string)($user['user_id'] ?? '') === $query) {
                    $matches = true;
                }

                // Check name (partial match)
                if (!$matches && strpos(strtolower($user['first_name'] ?? ''), $queryLower) !== false) {
                    $matches = true;
                }

                // Check username (partial match)
                if (!$matches && strpos(strtolower($user['username'] ?? ''), $queryLower) !== false) {
                    $matches = true;
                }

                if ($matches) {
                    $results[] = [
                        'user_id' => $user['user_id'] ?? 0,
                        'first_name' => $user['first_name'] ?? 'Unknown',
                        'username' => $user['username'] ?? '',
                        'balance' => $user['balance'] ?? 0,
                        'banned' => $user['banned'] ?? false,
                        'referrals' => count($user['promotion_report'] ?? []),
                        'total_withdrawn' => $user['successful_withdraw'] ?? 0
                    ];
                }
            }

            return $results;
        }, 300); // 5 minute cache for search results
    }
}
